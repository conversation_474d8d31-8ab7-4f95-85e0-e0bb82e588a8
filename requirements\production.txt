# Production dependencies
# Include base requirements
-r base.txt

# Production WSGI server
gunicorn>=21.2.0

# Production database (for future use)
psycopg2-binary>=2.9.0

# Monitoring and logging
sentry-sdk[flask]>=1.32.0

# Performance monitoring
newrelic>=9.0.0

# Security
flask-talisman>=1.1.0
flask-limiter>=3.5.0

# Caching
flask-caching>=2.1.0

# CORS handling
flask-cors>=4.0.0

# Environment variables validation
environs>=10.0.0

# Production utilities
supervisor>=4.2.0

# Health checks
flask-healthz>=0.0.3

# Metrics
prometheus-flask-exporter>=0.23.0

# SSL/TLS
certifi>=2023.7.22

# Production optimizations
brotli>=1.0.9
whitenoise>=6.5.0
