{% extends "base.html" %}

{% block title %}Error - YouTube Video Summarizer{% endblock %}

{% block meta_description %}An error occurred while processing your request. Please try again or contact support.{% endblock %}

{% block content %}
<div class="container">
    <div class="error-page">
        <div class="error-content">
            <!-- Error Icon and Title -->
            <div class="error-header">
                <div class="error-icon">
                    {% if error_code == 404 %}
                        🔍
                    {% elif error_code == 500 %}
                        ⚠️
                    {% elif error_code == 400 %}
                        ❌
                    {% else %}
                        😕
                    {% endif %}
                </div>
                <h1 class="error-title">
                    {% if error_code == 404 %}
                        Page Not Found
                    {% elif error_code == 500 %}
                        Server Error
                    {% elif error_code == 400 %}
                        Bad Request
                    {% else %}
                        Oops! Something went wrong
                    {% endif %}
                </h1>
            </div>

            <!-- Error Message -->
            <div class="error-message">
                {% if error_message %}
                    <p class="error-description">{{ error_message }}</p>
                {% else %}
                    <p class="error-description">
                        {% if error_code == 404 %}
                            The page you're looking for doesn't exist or has been moved.
                        {% elif error_code == 500 %}
                            We're experiencing technical difficulties. Please try again later.
                        {% elif error_code == 400 %}
                            There was a problem with your request. Please check your input and try again.
                        {% else %}
                            An unexpected error occurred. We're working to fix it.
                        {% endif %}
                    </p>
                {% endif %}
            </div>

            <!-- Error Details (for development) -->
            {% if config.DEBUG and error_details %}
            <div class="error-details">
                <h3>Error Details (Debug Mode)</h3>
                <pre class="error-traceback">{{ error_details }}</pre>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="error-actions">
                <a href="{{ url_for('index') }}" class="action-btn primary">
                    🏠 Go Home
                </a>
                <button onclick="history.back()" class="action-btn">
                    ← Go Back
                </button>
                <button onclick="location.reload()" class="action-btn">
                    🔄 Try Again
                </button>
            </div>

            <!-- Common Issues and Solutions -->
            <div class="error-help">
                <h3>Common Issues & Solutions</h3>
                <div class="help-grid">
                    <div class="help-item">
                        <h4>🔗 Invalid YouTube URL</h4>
                        <p>Make sure you're using a valid YouTube URL (youtube.com or youtu.be)</p>
                    </div>
                    <div class="help-item">
                        <h4>📝 No Captions Available</h4>
                        <p>The video must have English captions (auto-generated or manual)</p>
                    </div>
                    <div class="help-item">
                        <h4>🚫 Private or Restricted Video</h4>
                        <p>We can only process public videos that are accessible to everyone</p>
                    </div>
                    <div class="help-item">
                        <h4>⏱️ Video Too Long</h4>
                        <p>Very long videos may take more time to process or may not be supported</p>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="error-contact">
                <h3>Still Having Issues?</h3>
                <p>If the problem persists, please try the following:</p>
                <ul>
                    <li>Check your internet connection</li>
                    <li>Try a different YouTube video</li>
                    <li>Clear your browser cache and cookies</li>
                    <li>Disable browser extensions temporarily</li>
                </ul>
                {% if contact_email %}
                <p>For technical support, contact us at: 
                    <a href="mailto:{{ contact_email }}">{{ contact_email }}</a>
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
.error-page {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
    text-align: center;
}

.error-header {
    margin-bottom: 2rem;
}

.error-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.error-title {
    color: #e53e3e;
    margin-bottom: 1rem;
}

.error-message {
    margin-bottom: 2rem;
}

.error-description {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.6;
}

.error-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 1rem;
    margin: 2rem 0;
    text-align: left;
}

.error-traceback {
    background: #f1f3f4;
    padding: 1rem;
    border-radius: 3px;
    overflow-x: auto;
    font-size: 0.9rem;
}

.error-actions {
    margin: 2rem 0;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.error-help {
    margin: 3rem 0;
    text-align: left;
}

.help-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.help-item {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #5a67d8;
}

.help-item h4 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
}

.help-item p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
}

.error-contact {
    background: #e6fffa;
    border: 1px solid #81e6d9;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
    text-align: left;
}

.error-contact h3 {
    color: #234e52;
    margin-top: 0;
}

.error-contact ul {
    margin: 1rem 0;
}

.error-contact li {
    margin: 0.5rem 0;
}

@media (max-width: 768px) {
    .error-page {
        padding: 1rem;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .help-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
{% endblock %}
