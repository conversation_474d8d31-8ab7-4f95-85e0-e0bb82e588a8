# app/models/summary.py

from dataclasses import dataclass, field
from typing import Optional, Dict, List, Any
from datetime import datetime
from enum import Enum


class SummaryType(Enum):
    """Enumeration of summary types."""
    BASIC = "basic"
    ENHANCED = "enhanced"
    PROFESSIONAL = "professional"
    DETAILED = "detailed"


class SummaryStatus(Enum):
    """Enumeration of summary processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CACHED = "cached"


@dataclass
class SummaryRequest:
    """Data model for summary generation requests."""
    
    url: str
    summary_type: SummaryType = SummaryType.ENHANCED
    max_length: Optional[int] = None
    include_keywords: bool = True
    include_timestamps: bool = False
    language: str = "en"
    user_preferences: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate request data after initialization."""
        if not self.url:
            raise ValueError("URL is required")
        
        if not self._is_valid_youtube_url(self.url):
            raise ValueError("Invalid YouTube URL")
        
        if isinstance(self.summary_type, str):
            try:
                self.summary_type = SummaryType(self.summary_type)
            except ValueError:
                self.summary_type = SummaryType.ENHANCED
        
        if self.max_length is not None and self.max_length < 50:
            raise ValueError("Maximum length must be at least 50 characters")
    
    @staticmethod
    def _is_valid_youtube_url(url: str) -> bool:
        """Validate YouTube URL format."""
        import re
        youtube_patterns = [
            r'^https?://(www\.)?youtube\.com/watch\?v=[\w-]+',
            r'^https?://(www\.)?youtu\.be/[\w-]+',
            r'^https?://(www\.)?youtube\.com/embed/[\w-]+',
        ]
        return any(re.match(pattern, url.strip()) for pattern in youtube_patterns)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary representation."""
        return {
            'url': self.url,
            'summary_type': self.summary_type.value,
            'max_length': self.max_length,
            'include_keywords': self.include_keywords,
            'include_timestamps': self.include_timestamps,
            'language': self.language,
            'user_preferences': self.user_preferences
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SummaryRequest':
        """Create SummaryRequest from dictionary."""
        return cls(
            url=data.get('url', ''),
            summary_type=SummaryType(data.get('summary_type', 'enhanced')),
            max_length=data.get('max_length'),
            include_keywords=data.get('include_keywords', True),
            include_timestamps=data.get('include_timestamps', False),
            language=data.get('language', 'en'),
            user_preferences=data.get('user_preferences', {})
        )


@dataclass
class Summary:
    """Data model for generated summaries."""
    
    content: str
    keywords: List[str] = field(default_factory=list)
    summary_type: SummaryType = SummaryType.ENHANCED
    word_count: int = 0
    estimated_reading_time: int = 1  # in minutes
    confidence_score: Optional[float] = None  # 0.0 to 1.0
    key_points: List[str] = field(default_factory=list)
    content_type: str = "general"
    created_at: datetime = field(default_factory=datetime.now)
    processing_time: Optional[float] = None  # in seconds
    
    def __post_init__(self):
        """Calculate derived fields after initialization."""
        if self.content:
            self.word_count = len(self.content.split())
            self.estimated_reading_time = max(1, self.word_count // 200)  # 200 WPM
        
        if isinstance(self.summary_type, str):
            try:
                self.summary_type = SummaryType(self.summary_type)
            except ValueError:
                self.summary_type = SummaryType.ENHANCED
    
    @property
    def keywords_string(self) -> str:
        """Get keywords as comma-separated string."""
        return ", ".join(self.keywords) if self.keywords else ""
    
    @property
    def is_valid(self) -> bool:
        """Check if summary has valid content."""
        return (
            bool(self.content) and
            len(self.content.strip()) > 20 and
            self.content != "Summary not available."
        )
    
    def get_preview(self, max_length: int = 150) -> str:
        """Get a preview of the summary content."""
        if not self.content:
            return "No summary available"
        
        if len(self.content) <= max_length:
            return self.content
        
        # Find a good breaking point
        preview = self.content[:max_length]
        last_space = preview.rfind(' ')
        
        if last_space > max_length * 0.7:
            return preview[:last_space] + "..."
        else:
            return preview + "..."
    
    def add_keyword(self, keyword: str) -> None:
        """Add a keyword if not already present."""
        if keyword and keyword not in self.keywords:
            self.keywords.append(keyword)
    
    def add_key_point(self, point: str) -> None:
        """Add a key point if not already present."""
        if point and point not in self.key_points:
            self.key_points.append(point)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary representation."""
        return {
            'content': self.content,
            'keywords': self.keywords,
            'summary_type': self.summary_type.value,
            'word_count': self.word_count,
            'estimated_reading_time': self.estimated_reading_time,
            'confidence_score': self.confidence_score,
            'key_points': self.key_points,
            'content_type': self.content_type,
            'created_at': self.created_at.isoformat(),
            'processing_time': self.processing_time,
            'keywords_string': self.keywords_string,
            'is_valid': self.is_valid
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Summary':
        """Create Summary from dictionary."""
        created_at = data.get('created_at')
        if isinstance(created_at, str):
            created_at = datetime.fromisoformat(created_at)
        elif created_at is None:
            created_at = datetime.now()
        
        return cls(
            content=data.get('content', ''),
            keywords=data.get('keywords', []),
            summary_type=SummaryType(data.get('summary_type', 'enhanced')),
            confidence_score=data.get('confidence_score'),
            key_points=data.get('key_points', []),
            content_type=data.get('content_type', 'general'),
            created_at=created_at,
            processing_time=data.get('processing_time')
        )


@dataclass
class SummaryResponse:
    """Data model for API responses containing summary data."""
    
    summary: Summary
    video_info: Dict[str, str]
    status: SummaryStatus = SummaryStatus.COMPLETED
    error_message: Optional[str] = None
    cached: bool = False
    processing_stats: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate response data after initialization."""
        if isinstance(self.status, str):
            try:
                self.status = SummaryStatus(self.status)
            except ValueError:
                self.status = SummaryStatus.COMPLETED
        
        if not isinstance(self.summary, Summary):
            raise ValueError("summary must be a Summary instance")
    
    @property
    def is_success(self) -> bool:
        """Check if the response indicates success."""
        return self.status in [SummaryStatus.COMPLETED, SummaryStatus.CACHED] and not self.error_message
    
    def to_dict(self) -> Dict:
        """Convert to dictionary representation."""
        return {
            'summary': self.summary.content,
            'keywords': self.summary.keywords_string,
            'video_info': self.video_info,
            'status': self.status.value,
            'error_message': self.error_message,
            'cached': self.cached,
            'processing_stats': self.processing_stats,
            'summary_details': self.summary.to_dict()
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SummaryResponse':
        """Create SummaryResponse from dictionary."""
        summary_data = data.get('summary_details', {})
        if not summary_data and 'summary' in data:
            # Fallback for simple format
            summary_data = {
                'content': data.get('summary', ''),
                'keywords': data.get('keywords', '').split(', ') if data.get('keywords') else []
            }
        
        summary = Summary.from_dict(summary_data)
        
        return cls(
            summary=summary,
            video_info=data.get('video_info', {}),
            status=SummaryStatus(data.get('status', 'completed')),
            error_message=data.get('error_message'),
            cached=data.get('cached', False),
            processing_stats=data.get('processing_stats', {})
        )
    
    @classmethod
    def create_error_response(cls, error_message: str, video_info: Dict[str, str] = None) -> 'SummaryResponse':
        """Create an error response."""
        empty_summary = Summary(content="", summary_type=SummaryType.BASIC)
        return cls(
            summary=empty_summary,
            video_info=video_info or {},
            status=SummaryStatus.FAILED,
            error_message=error_message
        )
