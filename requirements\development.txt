# Development dependencies
# Include base requirements
-r base.txt

# Testing framework
pytest>=7.4.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
pytest-flask>=1.2.0

# Code quality and formatting
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.5.0

# Development tools
ipython>=8.14.0
jupyter>=1.0.0

# Debugging
pdb++>=0.10.3
ipdb>=0.13.13

# Documentation
sphinx>=7.1.0
sphinx-rtd-theme>=1.3.0

# Development server
flask-debugtoolbar>=0.13.1

# Environment management
python-decouple>=3.8

# Performance profiling
memory-profiler>=0.61.0
line-profiler>=4.1.0

# Database tools (for future use)
flask-migrate>=4.0.0
flask-sqlalchemy>=3.0.0

# API development tools
flask-restx>=1.2.0
marshmallow>=3.20.0

# Development utilities
watchdog>=3.0.0
pre-commit>=3.3.0

# Load testing
locust>=2.16.0

# Mock services
responses>=0.23.0
httpretty>=1.1.4
