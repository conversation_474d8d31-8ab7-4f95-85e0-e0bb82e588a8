# app/models/video.py

from dataclasses import dataclass, field
from typing import Optional, Dict, List
from datetime import datetime
import re


@dataclass
class VideoInfo:
    """Data model for YouTube video information."""
    
    title: str
    channel: str
    description: str = ""
    video_id: Optional[str] = None
    url: Optional[str] = None
    duration: Optional[int] = None  # Duration in seconds
    view_count: Optional[int] = None
    upload_date: Optional[str] = None
    thumbnail_url: Optional[str] = None
    
    def __post_init__(self):
        """Validate and clean data after initialization."""
        self.title = self._clean_text(self.title) if self.title else "Unknown Title"
        self.channel = self._clean_text(self.channel) if self.channel else "Unknown Channel"
        self.description = self._clean_text(self.description) if self.description else ""
        
        # Extract video ID from URL if provided
        if self.url and not self.video_id:
            self.video_id = self._extract_video_id(self.url)
    
    @staticmethod
    def _clean_text(text: str) -> str:
        """Clean and normalize text content."""
        if not text:
            return ""
        
        # Remove extra whitespace and normalize
        cleaned = re.sub(r'\s+', ' ', text.strip())
        
        # Remove or replace problematic characters
        cleaned = cleaned.replace('\n', ' ').replace('\r', ' ')
        
        return cleaned
    
    @staticmethod
    def _extract_video_id(url: str) -> Optional[str]:
        """Extract video ID from YouTube URL."""
        if not url:
            return None
        
        patterns = [
            r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([^&\n?#]+)',
            r'youtube\.com/v/([^&\n?#]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def to_dict(self) -> Dict:
        """Convert to dictionary representation."""
        return {
            'title': self.title,
            'channel': self.channel,
            'description': self.description,
            'video_id': self.video_id,
            'url': self.url,
            'duration': self.duration,
            'view_count': self.view_count,
            'upload_date': self.upload_date,
            'thumbnail_url': self.thumbnail_url
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'VideoInfo':
        """Create VideoInfo from dictionary."""
        return cls(
            title=data.get('title', 'Unknown Title'),
            channel=data.get('channel', 'Unknown Channel'),
            description=data.get('description', ''),
            video_id=data.get('video_id'),
            url=data.get('url'),
            duration=data.get('duration'),
            view_count=data.get('view_count'),
            upload_date=data.get('upload_date'),
            thumbnail_url=data.get('thumbnail_url')
        )


@dataclass
class Video:
    """Data model for a YouTube video with transcript and metadata."""
    
    info: VideoInfo
    transcript: str = ""
    transcript_language: str = "en"
    transcript_source: str = "auto"  # "auto", "manual", "api"
    processed_at: datetime = field(default_factory=datetime.now)
    processing_duration: Optional[float] = None  # Processing time in seconds
    
    def __post_init__(self):
        """Validate and clean data after initialization."""
        self.transcript = self._clean_transcript(self.transcript)
        
        if not isinstance(self.info, VideoInfo):
            raise ValueError("info must be a VideoInfo instance")
    
    @staticmethod
    def _clean_transcript(transcript: str) -> str:
        """Clean and normalize transcript text."""
        if not transcript:
            return ""
        
        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', transcript.strip())
        
        # Remove common transcript artifacts
        cleaned = re.sub(r'\[.*?\]', '', cleaned)  # Remove [Music], [Applause], etc.
        cleaned = re.sub(r'\(.*?\)', '', cleaned)  # Remove (inaudible), etc.
        
        # Remove timestamps if present
        cleaned = re.sub(r'\d{1,2}:\d{2}(?::\d{2})?\s*', '', cleaned)
        
        return cleaned
    
    @property
    def word_count(self) -> int:
        """Get word count of transcript."""
        return len(self.transcript.split()) if self.transcript else 0
    
    @property
    def estimated_reading_time(self) -> int:
        """Get estimated reading time in minutes (assuming 200 WPM)."""
        return max(1, self.word_count // 200)
    
    @property
    def is_valid(self) -> bool:
        """Check if video has valid data for processing."""
        return (
            self.info.title != "Unknown Title" and
            self.transcript and
            len(self.transcript.strip()) > 50  # Minimum transcript length
        )
    
    def get_content_preview(self, max_length: int = 200) -> str:
        """Get a preview of the transcript content."""
        if not self.transcript:
            return "No transcript available"
        
        if len(self.transcript) <= max_length:
            return self.transcript
        
        # Find a good breaking point (end of sentence)
        preview = self.transcript[:max_length]
        last_period = preview.rfind('.')
        
        if last_period > max_length * 0.7:  # If period is reasonably close to end
            return preview[:last_period + 1]
        else:
            return preview + "..."
    
    def to_dict(self) -> Dict:
        """Convert to dictionary representation."""
        return {
            'info': self.info.to_dict(),
            'transcript': self.transcript,
            'transcript_language': self.transcript_language,
            'transcript_source': self.transcript_source,
            'processed_at': self.processed_at.isoformat(),
            'processing_duration': self.processing_duration,
            'word_count': self.word_count,
            'estimated_reading_time': self.estimated_reading_time,
            'is_valid': self.is_valid
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Video':
        """Create Video from dictionary."""
        info_data = data.get('info', {})
        info = VideoInfo.from_dict(info_data)
        
        processed_at = data.get('processed_at')
        if isinstance(processed_at, str):
            processed_at = datetime.fromisoformat(processed_at)
        elif processed_at is None:
            processed_at = datetime.now()
        
        return cls(
            info=info,
            transcript=data.get('transcript', ''),
            transcript_language=data.get('transcript_language', 'en'),
            transcript_source=data.get('transcript_source', 'auto'),
            processed_at=processed_at,
            processing_duration=data.get('processing_duration')
        )
    
    def validate(self) -> List[str]:
        """
        Validate video data and return list of validation errors.
        
        Returns:
            List[str]: List of validation error messages
        """
        errors = []
        
        if not self.info.title or self.info.title == "Unknown Title":
            errors.append("Video title is missing or unknown")
        
        if not self.info.channel or self.info.channel == "Unknown Channel":
            errors.append("Video channel is missing or unknown")
        
        if not self.transcript:
            errors.append("Video transcript is missing")
        elif len(self.transcript.strip()) < 50:
            errors.append("Video transcript is too short for meaningful summarization")
        
        if self.word_count > 50000:  # Very long transcript
            errors.append("Video transcript is extremely long and may cause processing issues")
        
        return errors
