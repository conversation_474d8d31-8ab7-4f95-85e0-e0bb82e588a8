/* YouTube Video Summarizer Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    line-height: 1.6;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar {
    padding: 1rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    text-decoration: none;
    color: inherit;
}

.nav-brand h1 {
    color: #5a67d8;
    font-size: 1.5rem;
    font-weight: 700;
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #4a5568;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-link:hover {
    color: #5a67d8;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 0;
}

/* Container Styles */
.container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

/* Hero Section */
.hero-section {
    text-align: center;
    margin-bottom: 3rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: #4a5568;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Input Section */
.input-section {
    margin-bottom: 2rem;
}

.url-form {
    width: 100%;
}

.input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.url-input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.url-input:focus {
    outline: none;
    border-color: #5a67d8;
    box-shadow: 0 0 0 3px rgba(90, 103, 216, 0.1);
}

.summarize-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    white-space: nowrap;
}

.summarize-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(90, 103, 216, 0.3);
}

.summarize-btn:disabled {
    background: #a0aec0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.input-help {
    text-align: center;
    margin-top: 0.5rem;
}

.input-help small {
    color: #718096;
    font-size: 0.9rem;
}

/* Spinner Styles */
.spinner-container {
    text-align: center;
    margin: 2rem 0;
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #5a67d8;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.spinner-text {
    color: #4a5568;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Section */
.results-section {
    margin-top: 3rem;
    text-align: left;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.results-header h2 {
    color: #2d3748;
    font-size: 1.8rem;
    margin: 0;
}

.results-actions {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    color: #4a5568;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
}

.action-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: transparent;
}

.action-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(90, 103, 216, 0.3);
}

.summary-content {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    border-left: 4px solid #5a67d8;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.summary-text {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #2d3748;
}

/* Keywords Section */
.keywords-section {
    margin: 2rem 0;
}

.keywords-section h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.keywords-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.keyword-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.keywords-text {
    color: #4a5568;
    font-size: 1rem;
    line-height: 1.6;
}

/* Features Section */
.features-section {
    margin-top: 4rem;
    text-align: center;
}

.features-section h2 {
    color: #2d3748;
    font-size: 2rem;
    margin-bottom: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: white;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #2d3748;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.feature-card p {
    color: #4a5568;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid #e2e8f0;
    padding: 2rem 0;
    margin-top: auto;
}

.footer-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.footer-content p {
    color: #4a5568;
    margin-bottom: 1rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-link {
    color: #4a5568;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #5a67d8;
}

/* Error styles */
.error-message {
    background-color: #fed7d7;
    color: #c53030;
    padding: 1rem;
    border-radius: 5px;
    margin: 1rem 0;
    border-left: 4px solid #e53e3e;
}

/* Success styles */
.success-message {
    background-color: #c6f6d5;
    color: #2d7d32;
    padding: 1rem;
    border-radius: 5px;
    margin: 1rem 0;
    border-left: 4px solid #38a169;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    input[type="text"] {
        width: calc(100% - 16px);
        padding: 8px;
    }
    
    button {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
}
